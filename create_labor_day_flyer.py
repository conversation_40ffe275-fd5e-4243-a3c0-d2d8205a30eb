#!/usr/bin/env python3
"""
Labor Day Flyer Generator
Creates a professional Labor Day flyer with American flag design
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_american_flag(width, height):
    """Create an American flag image"""
    flag = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(flag)
    
    # Flag colors
    red = (179, 25, 66)
    blue = (60, 59, 110)
    white = (255, 255, 255)
    
    # Draw stripes (13 stripes total)
    stripe_height = height // 13
    for i in range(13):
        if i % 2 == 0:  # Red stripes (0, 2, 4, 6, 8, 10, 12)
            y = i * stripe_height
            draw.rectangle([0, y, width, y + stripe_height], fill=red)
    
    # Draw blue canton (union)
    canton_width = int(width * 0.4)
    canton_height = int(height * 7/13)  # 7 stripes high
    draw.rectangle([0, 0, canton_width, canton_height], fill=blue)
    
    # Add stars (simplified - just white dots)
    star_rows = 9
    stars_per_row = [6, 5, 6, 5, 6, 5, 6, 5, 6]  # Alternating pattern
    
    for row in range(star_rows):
        stars_in_row = stars_per_row[row]
        y = (canton_height / (star_rows + 1)) * (row + 1)
        
        for star in range(stars_in_row):
            if stars_in_row == 6:
                x = (canton_width / 7) * (star + 1)
            else:
                x = (canton_width / 6) * (star + 0.5) + (canton_width / 12)
            
            # Draw star as a circle (simplified)
            star_size = 8
            draw.ellipse([x-star_size//2, y-star_size//2, 
                         x+star_size//2, y+star_size//2], fill=white)
    
    return flag

def create_labor_day_flyer():
    """Create the main Labor Day flyer"""
    # Flyer dimensions (8.5" x 11" at 300 DPI)
    width, height = 2550, 3300
    
    # Create base image with gradient background
    flyer = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(flyer)
    
    # Create gradient background
    for y in range(height):
        # Subtle blue to white gradient
        color_value = int(240 + (15 * y / height))
        color = (color_value, color_value, 255)
        draw.line([(0, y), (width, y)], fill=color)
    
    # Add subtle patriotic pattern overlay
    pattern_size = 100
    for x in range(0, width, pattern_size):
        for y in range(0, height, pattern_size):
            if (x // pattern_size + y // pattern_size) % 2 == 0:
                overlay_color = (250, 245, 245, 30)  # Very light red
                draw.rectangle([x, y, x + pattern_size//2, y + pattern_size//2], 
                             fill=(252, 248, 248))
    
    # Try to load fonts, fall back to default if not available
    try:
        title_font = ImageFont.truetype("arial.ttf", 120)  # ~28pt equivalent
        main_font = ImageFont.truetype("arial.ttf", 100)   # ~24pt equivalent  
        footer_font = ImageFont.truetype("arial.ttf", 75)  # ~18pt equivalent
    except:
        try:
            title_font = ImageFont.truetype("calibri.ttf", 120)
            main_font = ImageFont.truetype("calibri.ttf", 100)
            footer_font = ImageFont.truetype("calibri.ttf", 75)
        except:
            # Fallback to default font
            title_font = ImageFont.load_default()
            main_font = ImageFont.load_default()
            footer_font = ImageFont.load_default()
    
    # Colors
    dark_blue = (44, 62, 80)
    red = (220, 53, 69)
    
    # Create and place American flag
    flag_width, flag_height = 300, 200
    flag = create_american_flag(flag_width, flag_height)
    
    # Header section
    header_y = 200
    
    # Place flag on the left side of header
    flag_x = width // 2 - 400
    flyer.paste(flag, (flag_x, header_y))
    
    # Add title next to flag
    title_text = "Happy Labor Day!"
    title_x = flag_x + flag_width + 50
    title_y = header_y + 50
    draw.text((title_x, title_y), title_text, font=title_font, fill=red)
    
    # Main message section
    main_y = height // 2 - 100
    main_text = "Please note that we will be closed on"
    date_text = "[September 2, 2024]"
    
    # Center the main message
    main_bbox = draw.textbbox((0, 0), main_text, font=main_font)
    main_width = main_bbox[2] - main_bbox[0]
    main_x = (width - main_width) // 2
    
    date_bbox = draw.textbbox((0, 0), date_text, font=main_font)
    date_width = date_bbox[2] - date_bbox[0]
    date_x = (width - date_width) // 2
    
    # Draw main message with background
    padding = 50
    bg_x1 = min(main_x, date_x) - padding
    bg_y1 = main_y - padding
    bg_x2 = max(main_x + main_width, date_x + date_width) + padding
    bg_y2 = main_y + 200 + padding
    
    # Draw background rectangle with border
    draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill=(255, 255, 255, 240))
    draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], outline=red, width=5)
    
    # Draw text
    draw.text((main_x, main_y), main_text, font=main_font, fill=dark_blue)
    draw.text((date_x, main_y + 120), date_text, font=main_font, fill=red)
    
    # Footer section
    footer_y = height - 400
    footer_text = "Enjoy a well-deserved break and\ncelebrate with your loved ones!"
    
    # Center footer text
    lines = footer_text.split('\n')
    for i, line in enumerate(lines):
        line_bbox = draw.textbbox((0, 0), line, font=footer_font)
        line_width = line_bbox[2] - line_bbox[0]
        line_x = (width - line_width) // 2
        draw.text((line_x, footer_y + i * 90), line, font=footer_font, fill=dark_blue)
    
    # Add decorative stars
    star_font_size = 60
    try:
        star_font = ImageFont.truetype("arial.ttf", star_font_size)
    except:
        star_font = ImageFont.load_default()
    
    star_color = (255, 193, 7)  # Gold color
    
    # Top right stars
    draw.text((width - 200, 50), "★ ★ ★", font=star_font, fill=star_color)
    
    # Bottom left stars  
    draw.text((50, height - 150), "★ ★ ★", font=star_font, fill=star_color)
    
    return flyer

def main():
    """Main function to create and save the flyer"""
    print("Creating Labor Day flyer...")
    
    try:
        flyer = create_labor_day_flyer()
        
        # Save the flyer
        output_path = "Labor_Day_Flyer.png"
        flyer.save(output_path, "PNG", quality=95, dpi=(300, 300))
        
        print(f"✅ Labor Day flyer created successfully!")
        print(f"📁 Saved as: {output_path}")
        print(f"📏 Size: 8.5\" x 11\" at 300 DPI")
        print(f"🖨️  Ready for printing!")
        
        # Also save as JPEG for smaller file size
        jpeg_path = "Labor_Day_Flyer.jpg"
        flyer.save(jpeg_path, "JPEG", quality=95, dpi=(300, 300))
        print(f"📁 Also saved as: {jpeg_path}")
        
    except Exception as e:
        print(f"❌ Error creating flyer: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
