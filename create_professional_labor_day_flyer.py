#!/usr/bin/env python3
"""
Professional Labor Day Flyer Generator
Creates a clean, professional Labor Day flyer following exact specifications
"""

from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import Color
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch

def create_american_flag(c, x, y, width, height):
    """Draw a detailed American flag"""
    # Flag colors
    flag_red = Color(0.698, 0.098, 0.259)  # #B22234
    flag_blue = Color(0.235, 0.231, 0.431)  # #3C3B6E
    flag_white = Color(1, 1, 1)
    
    # Draw flag border
    c.setStrokeColor(Color(0.5, 0.5, 0.5))
    c.setLineWidth(1)
    c.rect(x, y, width, height, fill=0, stroke=1)
    
    # Draw 13 stripes
    stripe_height = height / 13
    for i in range(13):
        stripe_y = y + height - (i + 1) * stripe_height
        if i % 2 == 0:  # Red stripes (0, 2, 4, 6, 8, 10, 12)
            c.setFillColor(flag_red)
        else:  # White stripes
            c.setFillColor(flag_white)
        c.rect(x, stripe_y, width, stripe_height, fill=1, stroke=0)
    
    # Draw blue canton (union)
    canton_width = width * 0.4
    canton_height = height * 7/13  # 7 stripes high
    c.setFillColor(flag_blue)
    c.rect(x, y + height - canton_height, canton_width, canton_height, fill=1, stroke=0)
    
    # Add stars (50 stars in 9 rows)
    c.setFillColor(flag_white)
    star_size = 1.5
    rows = 9
    stars_per_row = [6, 5, 6, 5, 6, 5, 6, 5, 6]  # Alternating 6 and 5 stars
    
    for row in range(rows):
        stars_in_row = stars_per_row[row]
        star_y = y + height - canton_height + (canton_height / (rows + 1)) * (row + 1)
        
        for star in range(stars_in_row):
            if stars_in_row == 6:
                star_x = x + (canton_width / 7) * (star + 1)
            else:
                star_x = x + (canton_width / 6) * (star + 0.5) + (canton_width / 12)
            
            # Draw star as a small circle (simplified)
            c.circle(star_x, star_y, star_size, fill=1, stroke=0)

def create_professional_labor_day_flyer():
    """Create the professional Labor Day flyer"""
    filename = "Professional_Labor_Day_Flyer.pdf"
    
    # Create canvas - 8.5 x 11 inches
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter  # 612 x 792 points
    
    # Color scheme
    patriot_red = Color(0.698, 0.098, 0.259)  # #B22234
    patriot_blue = Color(0.235, 0.231, 0.431)  # #3C3B6E
    dark_text = Color(0.2, 0.2, 0.2)
    subtle_grey = Color(0.96, 0.96, 0.96)
    
    # Background - subtle grey
    c.setFillColor(subtle_grey)
    c.rect(0, 0, width, height, fill=1, stroke=0)
    
    # Add subtle patriotic accents (thin border lines)
    c.setStrokeColor(patriot_red)
    c.setLineWidth(3)
    c.line(0, height - 10, width, height - 10)  # Top red line
    
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(3)
    c.line(0, 10, width, 10)  # Bottom blue line
    
    # HEADER SECTION
    header_y = height - 120
    
    # Small American flag icon on the left side of header
    flag_width = 60
    flag_height = 40
    flag_x = width/2 - 200  # Left side of center
    flag_y = header_y - flag_height/2
    
    create_american_flag(c, flag_x, flag_y, flag_width, flag_height)
    
    # "Happy Labor Day!" text next to flag - bold, 24-28 points
    c.setFillColor(patriot_red)
    c.setFont("Helvetica-Bold", 28)
    title_x = flag_x + flag_width + 20
    c.drawString(title_x, header_y, "Happy Labor Day!")
    
    # MAIN MESSAGE SECTION
    main_y = height/2 + 50
    
    # Center-aligned main message - 20-24 points
    c.setFillColor(dark_text)
    c.setFont("Helvetica", 24)
    
    main_text = "Please note that we will be closed on"
    main_text_width = c.stringWidth(main_text, "Helvetica", 24)
    main_text_x = (width - main_text_width) / 2
    c.drawString(main_text_x, main_y, main_text)
    
    # Editable date field - highlighted for easy editing
    date_y = main_y - 50
    date_text = "[September 2, 2024]"
    
    # Create highlighted box for date
    c.setFont("Helvetica-Bold", 22)
    date_text_width = c.stringWidth(date_text, "Helvetica-Bold", 22)
    date_x = (width - date_text_width) / 2
    
    # Yellow highlight box for editing
    highlight_padding = 10
    c.setFillColor(Color(1, 1, 0.7))  # Light yellow
    c.setStrokeColor(patriot_red)
    c.setLineWidth(2)
    c.rect(date_x - highlight_padding, date_y - 5, 
           date_text_width + 2*highlight_padding, 35, fill=1, stroke=1)
    
    # Date text
    c.setFillColor(patriot_red)
    c.drawString(date_x, date_y, date_text)
    
    # AMERICAN FLAG PLACEMENT - Right side, halfway down
    large_flag_width = 120
    large_flag_height = 80
    large_flag_x = width - large_flag_width - 50  # Right side with margin
    large_flag_y = height/2 - large_flag_height/2  # Halfway down
    
    create_american_flag(c, large_flag_x, large_flag_y, large_flag_width, large_flag_height)
    
    # FOOTER SECTION
    footer_y = 150
    
    # Footer message - 16-18 points
    c.setFillColor(dark_text)
    c.setFont("Helvetica", 18)
    
    footer_text = "Enjoy a well-deserved break and celebrate with your loved ones!"
    footer_text_width = c.stringWidth(footer_text, "Helvetica", 18)
    footer_x = (width - footer_text_width) / 2
    c.drawString(footer_x, footer_y, footer_text)
    
    # ADDITIONAL EDITABLE FIELDS
    
    # Business name field
    business_y = height - 60
    c.setFillColor(dark_text)
    c.setFont("Helvetica", 12)
    c.drawString(50, business_y, "Business Name:")
    
    # Business name editable box
    business_box_x = 150
    business_box_width = 250
    business_box_height = 20
    
    c.setFillColor(Color(1, 1, 0.7))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(1)
    c.rect(business_box_x, business_y - 5, business_box_width, business_box_height, fill=1, stroke=1)
    
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 11)
    c.drawString(business_box_x + 5, business_y, "Your Business Name Here")
    
    # Contact information field
    contact_y = 100
    c.setFillColor(dark_text)
    c.setFont("Helvetica", 12)
    c.drawString(50, contact_y, "Contact Information:")
    
    # Contact info editable box
    contact_box_x = 50
    contact_box_y = contact_y - 25
    contact_box_width = 400
    contact_box_height = 20
    
    c.setFillColor(Color(1, 1, 0.7))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(1)
    c.rect(contact_box_x, contact_box_y, contact_box_width, contact_box_height, fill=1, stroke=1)
    
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 11)
    c.drawString(contact_box_x + 5, contact_box_y + 5, "Phone: (************* | Email: <EMAIL>")
    
    # Red, white, and blue accent elements
    # Small decorative elements in corners
    accent_size = 15
    
    # Top left accent - blue
    c.setFillColor(patriot_blue)
    c.rect(20, height - 40, accent_size, accent_size, fill=1, stroke=0)
    
    # Top right accent - red  
    c.setFillColor(patriot_red)
    c.rect(width - 35, height - 40, accent_size, accent_size, fill=1, stroke=0)
    
    # Bottom left accent - red
    c.setFillColor(patriot_red)
    c.rect(20, 25, accent_size, accent_size, fill=1, stroke=0)
    
    # Bottom right accent - blue
    c.setFillColor(patriot_blue)
    c.rect(width - 35, 25, accent_size, accent_size, fill=1, stroke=0)
    
    # Instructions for editing
    c.setFillColor(Color(0.5, 0.5, 0.5))
    c.setFont("Helvetica", 9)
    instruction_text = "Yellow highlighted areas can be edited using PDF editing software (Adobe Acrobat, etc.)"
    inst_width = c.stringWidth(instruction_text, "Helvetica", 9)
    c.drawString((width - inst_width)/2, 50, instruction_text)
    
    # Save the PDF
    c.save()
    return filename

def main():
    """Main function to create the professional flyer"""
    print("Creating professional Labor Day flyer...")
    
    try:
        filename = create_professional_labor_day_flyer()
        
        print(f"✅ Professional Labor Day flyer created successfully!")
        print(f"📁 Saved as: {filename}")
        print(f"📏 Size: 8.5\" x 11\" (Letter size)")
        print(f"🎨 Design features:")
        print(f"   • Clean patriotic color scheme (red, white, blue)")
        print(f"   • Subtle grey background")
        print(f"   • Small American flag in header (left side)")
        print(f"   • Large American flag on right side (halfway down)")
        print(f"   • Bold 'Happy Labor Day!' title (28pt)")
        print(f"   • Center-aligned main message (24pt)")
        print(f"   • Footer message (18pt)")
        print(f"   • Editable yellow-highlighted fields")
        print(f"   • Professional layout with patriotic accents")
        print(f"\n✏️  Editable fields:")
        print(f"   • Closure date (highlighted in yellow)")
        print(f"   • Business name")
        print(f"   • Contact information")
        print(f"\n📝 How to customize:")
        print(f"   1. Open PDF in Adobe Acrobat or similar editor")
        print(f"   2. Click on yellow highlighted areas to edit")
        print(f"   3. Update date, business name, and contact info")
        print(f"   4. Save and print!")
        
    except Exception as e:
        print(f"❌ Error creating flyer: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
