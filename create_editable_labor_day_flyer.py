#!/usr/bin/env python3
"""
Editable Labor Day Flyer PDF Generator
Creates a professional Labor Day flyer PDF with text placeholders for easy editing
"""

from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import Color, red, blue, white, black
from reportlab.lib.units import inch
from reportlab.pdfgen import canvas
from reportlab.graphics.shapes import Drawing, Rect, String, Circle
from reportlab.graphics import renderPDF
import os

def create_american_flag_drawing(width, height):
    """Create an American flag as a ReportLab drawing"""
    drawing = Drawing(width, height)
    
    # Flag colors
    flag_red = Color(179/255, 25/255, 66/255)
    flag_blue = Color(60/255, 59/255, 110/255)
    flag_white = Color(1, 1, 1)
    
    # Draw stripes (13 stripes total)
    stripe_height = height / 13
    for i in range(13):
        if i % 2 == 0:  # Red stripes
            y = height - (i + 1) * stripe_height
            stripe = Rect(0, y, width, stripe_height)
            stripe.fillColor = flag_red
            stripe.strokeColor = None
            drawing.add(stripe)
        else:  # White stripes
            y = height - (i + 1) * stripe_height
            stripe = Rect(0, y, width, stripe_height)
            stripe.fillColor = flag_white
            stripe.strokeColor = None
            drawing.add(stripe)
    
    # Draw blue canton (union)
    canton_width = width * 0.4
    canton_height = height * 7/13
    canton = Rect(0, height - canton_height, canton_width, canton_height)
    canton.fillColor = flag_blue
    canton.strokeColor = None
    drawing.add(canton)
    
    # Add stars (simplified as white circles)
    star_rows = 9
    stars_per_row = [6, 5, 6, 5, 6, 5, 6, 5, 6]
    star_size = 3
    
    for row in range(star_rows):
        stars_in_row = stars_per_row[row]
        y = height - canton_height + (canton_height / (star_rows + 1)) * (row + 1)
        
        for star in range(stars_in_row):
            if stars_in_row == 6:
                x = (canton_width / 7) * (star + 1)
            else:
                x = (canton_width / 6) * (star + 0.5) + (canton_width / 12)
            
            # Draw star as a circle
            from reportlab.graphics.shapes import Circle
            star_circle = Circle(x, y, star_size)
            star_circle.fillColor = flag_white
            star_circle.strokeColor = None
            drawing.add(star_circle)
    
    return drawing

def create_editable_labor_day_flyer():
    """Create the editable Labor Day flyer PDF"""
    filename = "Editable_Labor_Day_Flyer.pdf"

    # Create canvas
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Colors
    patriot_red = Color(220/255, 53/255, 69/255)
    patriot_blue = Color(44/255, 62/255, 80/255)
    gold = Color(255/255, 193/255, 7/255)
    
    # Background gradient effect (simplified)
    c.setFillColor(Color(0.95, 0.95, 1.0))
    c.rect(0, 0, width, height, fill=1, stroke=0)
    
    # Add subtle pattern
    c.setFillColor(Color(0.98, 0.96, 0.96))
    pattern_size = 30
    for x in range(0, int(width), pattern_size * 2):
        for y in range(0, int(height), pattern_size * 2):
            c.rect(x, y, pattern_size, pattern_size, fill=1, stroke=0)
    
    # Header Section
    header_y = height - 150
    
    # Draw American flag
    flag_width, flag_height = 120, 80
    flag_x = width/2 - 200
    flag_drawing = create_american_flag_drawing(flag_width, flag_height)
    renderPDF.draw(flag_drawing, c, flag_x, header_y - flag_height/2)
    
    # Title
    c.setFillColor(patriot_red)
    c.setFont("Helvetica-Bold", 36)
    title_x = flag_x + flag_width + 30
    c.drawString(title_x, header_y, "Happy Labor Day!")
    
    # Main Message Section
    main_y = height/2 + 50
    
    # Background box for main message
    box_width = 500
    box_height = 120
    box_x = (width - box_width) / 2
    box_y = main_y - 60
    
    c.setFillColor(Color(1, 1, 1, 0.9))
    c.setStrokeColor(patriot_red)
    c.setLineWidth(3)
    c.rect(box_x, box_y, box_width, box_height, fill=1, stroke=1)
    
    # Main message text
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 24)
    text_x = width/2 - 200
    c.drawString(text_x, main_y + 20, "Please note that we will be closed on")

    # Date placeholder with editable box
    date_field_x = width/2 - 100
    date_field_y = main_y - 20
    date_field_width = 200
    date_field_height = 30

    # Draw editable date box
    c.setFillColor(Color(1, 1, 1))
    c.setStrokeColor(patriot_red)
    c.setLineWidth(2)
    c.rect(date_field_x, date_field_y, date_field_width, date_field_height, fill=1, stroke=1)

    # Date text (can be edited in PDF editor)
    c.setFillColor(patriot_red)
    c.setFont("Helvetica-Bold", 20)
    date_text = "September 2, 2024"
    date_text_width = c.stringWidth(date_text, "Helvetica-Bold", 20)
    c.drawString(date_field_x + (date_field_width - date_text_width)/2, date_field_y + 5, date_text)
    
    # Footer Section
    footer_y = 200
    
    # Footer background
    footer_box_width = 600
    footer_box_height = 80
    footer_box_x = (width - footer_box_width) / 2
    footer_box_y = footer_y - 40
    
    c.setFillColor(Color(0.98, 0.98, 1.0, 0.8))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(2)
    c.rect(footer_box_x, footer_box_y, footer_box_width, footer_box_height, fill=1, stroke=1)
    
    # Footer text
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica-Oblique", 18)
    footer_text1 = "Enjoy a well-deserved break and"
    footer_text2 = "celebrate with your loved ones!"
    
    text1_width = c.stringWidth(footer_text1, "Helvetica-Oblique", 18)
    text2_width = c.stringWidth(footer_text2, "Helvetica-Oblique", 18)
    
    c.drawString((width - text1_width)/2, footer_y + 10, footer_text1)
    c.drawString((width - text2_width)/2, footer_y - 15, footer_text2)
    
    # Decorative stars
    c.setFillColor(gold)
    c.setFont("Helvetica", 24)
    c.drawString(width - 120, height - 50, "★ ★ ★")
    c.drawString(50, 50, "★ ★ ★")
    
    # Additional editable sections with placeholder boxes

    # Business name field
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 12)
    c.drawString(50, height - 50, "Business Name:")

    # Business name box
    c.setFillColor(Color(1, 1, 1))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(1)
    c.rect(150, height - 55, 200, 20, fill=1, stroke=1)
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 12)
    c.drawString(155, height - 50, "Your Business Name")

    # Contact info field
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 12)
    c.drawString(50, 100, "Contact Info:")

    # Contact info box
    c.setFillColor(Color(1, 1, 1))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(1)
    c.rect(150, 95, 300, 20, fill=1, stroke=1)
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 12)
    c.drawString(155, 100, "Phone: (************* | Email: <EMAIL>")

    # Additional message field
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 12)
    c.drawString(50, main_y - 100, "Additional Message:")

    # Additional message box
    c.setFillColor(Color(1, 1, 1))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(1)
    c.rect(50, main_y - 140, 500, 30, fill=1, stroke=1)
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 10)
    c.drawString(55, main_y - 130, "We appreciate your understanding and look forward to serving you when we reopen.")
    c.drawString(55, main_y - 120, "Thank you for your continued support!")
    
    # Add instructions text
    c.setFillColor(Color(0.5, 0.5, 0.5))
    c.setFont("Helvetica", 10)
    instruction_text = "Use a PDF editor (Adobe Acrobat, etc.) to edit the text in the white boxes above."
    text_width = c.stringWidth(instruction_text, "Helvetica", 10)
    c.drawString((width - text_width)/2, 30, instruction_text)
    
    # Save the PDF
    c.save()
    
    return filename

def main():
    """Main function to create the editable PDF flyer"""
    print("Creating editable Labor Day flyer PDF...")
    
    try:
        filename = create_editable_labor_day_flyer()
        
        print(f"✅ Editable Labor Day flyer PDF created successfully!")
        print(f"📁 Saved as: {filename}")
        print(f"📏 Size: 8.5\" x 11\" (Letter size)")
        print(f"✏️  Editable fields included:")
        print(f"   • Closure date")
        print(f"   • Business name") 
        print(f"   • Contact information")
        print(f"   • Additional message")
        print(f"🖨️  Ready for printing and editing!")
        print(f"\n📝 Instructions:")
        print(f"   1. Open the PDF in Adobe Reader, Foxit, or similar PDF viewer")
        print(f"   2. Click on the highlighted fields to edit them")
        print(f"   3. Save the PDF after making changes")
        print(f"   4. Print or share as needed")
        
    except Exception as e:
        print(f"❌ Error creating editable PDF: {e}")
        print("Installing required package...")
        return False
    
    return True

if __name__ == "__main__":
    main()
