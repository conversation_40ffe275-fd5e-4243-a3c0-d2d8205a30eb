<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Labor Day Flyer</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Open+Sans:wght@400;600&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .flyer {
            width: 8.5in;
            height: 11in;
            background: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 60px 40px;
        }
        
        .flyer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(45deg, rgba(220, 53, 69, 0.03) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(220, 53, 69, 0.03) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(0, 123, 255, 0.03) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(0, 123, 255, 0.03) 75%);
            background-size: 60px 60px;
            background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
            z-index: 1;
        }
        
        .content {
            position: relative;
            z-index: 2;
            text-align: center;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 40px;
        }
        
        .flag-icon {
            width: 40px;
            height: 30px;
            background: linear-gradient(to bottom, 
                #B22234 0%, #B22234 7.7%, 
                white 7.7%, white 15.4%, 
                #B22234 15.4%, #B22234 23.1%, 
                white 23.1%, white 30.8%, 
                #B22234 30.8%, #B22234 38.5%, 
                white 38.5%, white 46.2%, 
                #B22234 46.2%, #B22234 53.9%, 
                white 53.9%, white 61.6%, 
                #B22234 61.6%, #B22234 69.3%, 
                white 69.3%, white 77%, 
                #B22234 77%, #B22234 84.7%, 
                white 84.7%, white 92.4%, 
                #B22234 92.4%, #B22234 100%);
            position: relative;
            border: 1px solid #ddd;
            border-radius: 2px;
        }
        
        .flag-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 40%;
            height: 53.8%;
            background: #3C3B6E;
        }
        
        .header h1 {
            font-family: 'Merriweather', serif;
            font-size: 28pt;
            font-weight: 700;
            color: #2c3e50;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #dc3545, #0d6efd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .main-message {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .main-message p {
            font-family: 'Merriweather', serif;
            font-size: 24pt;
            font-weight: 400;
            color: #2c3e50;
            line-height: 1.4;
            max-width: 600px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            background-clip: padding-box;
            position: relative;
        }
        
        .main-message p::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #dc3545, #0d6efd, #dc3545);
            border-radius: 14px;
            z-index: -1;
        }
        
        .date-placeholder {
            color: #dc3545;
            font-weight: 600;
            text-decoration: underline;
        }
        
        .footer {
            margin-top: 40px;
        }
        
        .footer p {
            font-family: 'Open Sans', sans-serif;
            font-size: 18pt;
            font-weight: 400;
            color: #495057;
            line-height: 1.3;
            font-style: italic;
        }
        
        .decorative-stars {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 20px;
            color: #ffc107;
            opacity: 0.6;
        }
        
        .decorative-stars-bottom {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 20px;
            color: #ffc107;
            opacity: 0.6;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .flyer {
                box-shadow: none;
                border-radius: 0;
                width: 100%;
                height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="flyer">
        <div class="decorative-stars">★ ★ ★</div>
        <div class="decorative-stars-bottom">★ ★ ★</div>
        
        <div class="content">
            <div class="header">
                <div class="flag-icon"></div>
                <h1>Happy Labor Day!</h1>
            </div>
            
            <div class="main-message">
                <p>Please note that we will be closed on <span class="date-placeholder">[September 2, 2024]</span>.</p>
            </div>
            
            <div class="footer">
                <p>Enjoy a well-deserved break and celebrate with your loved ones!</p>
            </div>
        </div>
    </div>
</body>
</html>
