#!/usr/bin/env python3
"""
Simple Editable Labor Day Flyer Generator
Creates a Labor Day flyer PDF with clearly marked editable areas
"""

from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import Color
from reportlab.pdfgen import canvas
from reportlab.graphics.shapes import Drawing, Rect, Circle
from reportlab.graphics import renderPDF

def create_american_flag_simple(c, x, y, width, height):
    """Draw a simple American flag directly on canvas"""
    # Flag colors
    flag_red = Color(179/255, 25/255, 66/255)
    flag_blue = Color(60/255, 59/255, 110/255)
    
    # Draw white background
    c.setFillColor(Color(1, 1, 1))
    c.rect(x, y, width, height, fill=1, stroke=1)
    
    # Draw stripes
    stripe_height = height / 13
    for i in range(13):
        if i % 2 == 0:  # Red stripes
            stripe_y = y + height - (i + 1) * stripe_height
            c.setFillColor(flag_red)
            c.rect(x, stripe_y, width, stripe_height, fill=1, stroke=0)
    
    # Draw blue canton
    canton_width = width * 0.4
    canton_height = height * 7/13
    c.setFillColor(flag_blue)
    c.rect(x, y + height - canton_height, canton_width, canton_height, fill=1, stroke=0)
    
    # Add white stars (simplified as small circles)
    c.setFillColor(Color(1, 1, 1))
    star_size = 2
    rows = 9
    for row in range(rows):
        stars_in_row = 6 if row % 2 == 0 else 5
        star_y = y + height - canton_height + (canton_height / (rows + 1)) * (row + 1)
        
        for star in range(stars_in_row):
            if stars_in_row == 6:
                star_x = x + (canton_width / 7) * (star + 1)
            else:
                star_x = x + (canton_width / 6) * (star + 0.5) + (canton_width / 12)
            
            c.circle(star_x, star_y, star_size, fill=1, stroke=0)

def create_simple_editable_flyer():
    """Create a simple editable Labor Day flyer"""
    filename = "Simple_Editable_Labor_Day_Flyer.pdf"
    
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Colors
    patriot_red = Color(220/255, 53/255, 69/255)
    patriot_blue = Color(44/255, 62/255, 80/255)
    gold = Color(255/255, 193/255, 7/255)
    light_blue = Color(0.95, 0.95, 1.0)
    
    # Background
    c.setFillColor(light_blue)
    c.rect(0, 0, width, height, fill=1, stroke=0)
    
    # Title section
    title_y = height - 120
    
    # Draw American flag
    flag_width, flag_height = 100, 65
    flag_x = width/2 - 180
    create_american_flag_simple(c, flag_x, title_y - flag_height/2, flag_width, flag_height)
    
    # Title text
    c.setFillColor(patriot_red)
    c.setFont("Helvetica-Bold", 36)
    title_x = flag_x + flag_width + 20
    c.drawString(title_x, title_y, "Happy Labor Day!")
    
    # Main content box
    main_y = height/2 + 30
    box_width = 520
    box_height = 200
    box_x = (width - box_width) / 2
    box_y = main_y - 100
    
    # Draw main content background
    c.setFillColor(Color(1, 1, 1))
    c.setStrokeColor(patriot_red)
    c.setLineWidth(3)
    c.rect(box_x, box_y, box_width, box_height, fill=1, stroke=1)
    
    # Main message
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 24)
    c.drawString(box_x + 20, main_y + 40, "Please note that we will be closed on")
    
    # Editable date section
    c.setFont("Helvetica", 16)
    c.drawString(box_x + 20, main_y, "DATE:")
    
    # Date input box
    date_box_x = box_x + 80
    date_box_y = main_y - 10
    date_box_width = 200
    date_box_height = 35
    
    c.setFillColor(Color(1, 1, 0.8))  # Light yellow for editing
    c.setStrokeColor(patriot_red)
    c.setLineWidth(2)
    c.rect(date_box_x, date_box_y, date_box_width, date_box_height, fill=1, stroke=1)
    
    c.setFillColor(patriot_red)
    c.setFont("Helvetica-Bold", 18)
    c.drawString(date_box_x + 10, date_box_y + 8, "September 2, 2024")
    
    # Business name section
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 16)
    c.drawString(box_x + 20, main_y - 50, "BUSINESS:")
    
    business_box_x = box_x + 120
    business_box_y = main_y - 65
    business_box_width = 300
    business_box_height = 25
    
    c.setFillColor(Color(1, 1, 0.8))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(2)
    c.rect(business_box_x, business_box_y, business_box_width, business_box_height, fill=1, stroke=1)
    
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 14)
    c.drawString(business_box_x + 10, business_box_y + 5, "Your Business Name Here")
    
    # Footer section
    footer_y = 180
    footer_box_width = 500
    footer_box_height = 60
    footer_box_x = (width - footer_box_width) / 2
    
    c.setFillColor(Color(0.98, 0.98, 1.0))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(2)
    c.rect(footer_box_x, footer_y - 30, footer_box_width, footer_box_height, fill=1, stroke=1)
    
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica-Oblique", 18)
    footer_text1 = "Enjoy a well-deserved break and"
    footer_text2 = "celebrate with your loved ones!"
    
    text1_width = c.stringWidth(footer_text1, "Helvetica-Oblique", 18)
    text2_width = c.stringWidth(footer_text2, "Helvetica-Oblique", 18)
    
    c.drawString((width - text1_width)/2, footer_y + 5, footer_text1)
    c.drawString((width - text2_width)/2, footer_y - 20, footer_text2)
    
    # Contact info section
    contact_y = 120
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 12)
    c.drawString(50, contact_y, "CONTACT INFO:")
    
    contact_box_x = 150
    contact_box_y = contact_y - 15
    contact_box_width = 350
    contact_box_height = 20
    
    c.setFillColor(Color(1, 1, 0.8))
    c.setStrokeColor(patriot_blue)
    c.setLineWidth(1)
    c.rect(contact_box_x, contact_box_y, contact_box_width, contact_box_height, fill=1, stroke=1)
    
    c.setFillColor(patriot_blue)
    c.setFont("Helvetica", 11)
    c.drawString(contact_box_x + 5, contact_box_y + 4, "Phone: (************* | Email: <EMAIL>")
    
    # Decorative stars
    c.setFillColor(gold)
    c.setFont("Helvetica", 20)
    c.drawString(width - 100, height - 40, "★ ★ ★")
    c.drawString(40, 40, "★ ★ ★")
    
    # Instructions
    c.setFillColor(Color(0.4, 0.4, 0.4))
    c.setFont("Helvetica", 10)
    instruction1 = "EDITING INSTRUCTIONS: The yellow highlighted boxes can be edited using PDF editing software"
    instruction2 = "such as Adobe Acrobat, Foxit Reader, or similar programs. Simply click and type new text."
    
    inst1_width = c.stringWidth(instruction1, "Helvetica", 10)
    inst2_width = c.stringWidth(instruction2, "Helvetica", 10)
    
    c.drawString((width - inst1_width)/2, 70, instruction1)
    c.drawString((width - inst2_width)/2, 55, instruction2)
    
    # Save
    c.save()
    return filename

def main():
    """Main function"""
    print("Creating simple editable Labor Day flyer...")
    
    try:
        filename = create_simple_editable_flyer()
        
        print(f"✅ Simple editable Labor Day flyer created!")
        print(f"📁 Saved as: {filename}")
        print(f"📏 Size: 8.5\" x 11\" (Letter size)")
        print(f"✏️  Features:")
        print(f"   • American flag design")
        print(f"   • Highlighted editable areas (yellow boxes)")
        print(f"   • Professional patriotic layout")
        print(f"   • Clear editing instructions")
        print(f"\n📝 How to edit:")
        print(f"   1. Open PDF in Adobe Acrobat, Foxit, or similar editor")
        print(f"   2. Click on yellow highlighted areas to edit text")
        print(f"   3. Change date, business name, and contact info")
        print(f"   4. Save and print!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
